<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
    </div>

    <!-- 分析详情 -->
    <div v-else-if="analysis" class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button
          @click="goBack"
          class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回分析工作台
        </button>
      </div>

      <!-- 视频信息头部 -->
      <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
          <div class="flex justify-between items-start">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">{{ analysis.video.filename }}</h1>
              <p class="mt-2 text-gray-600">{{ formatDuration(analysis.video.duration) }} | {{ analysis.video.resolution }}</p>
            </div>
            <div class="flex space-x-3">
              <button
                @click="generateClips"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                生成片段
              </button>
              <button
                @click="exportReport"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                导出报告
              </button>
            </div>
          </div>
          
          <!-- 分析进度 -->
          <div class="mt-4">
            <div class="flex justify-between items-center text-sm text-gray-500 mb-1">
              <span>分析状态</span>
              <div class="flex items-center space-x-2">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(analysis.video.status)"
                >
                  {{ getStatusText(analysis.video.status) }}
                </span>
                <span v-if="analysis.video.status !== 'failed'">{{ Math.round(analysis.progress) }}%</span>
              </div>
            </div>

            <!-- 进度条 -->
            <div v-if="analysis.video.status !== 'failed'" class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-primary-600 h-2 rounded-full transition-all"
                :style="{ width: `${analysis.progress}%` }"
              ></div>
            </div>

            <!-- 失败状态和重试按钮 -->
            <div v-if="analysis.video.status === 'failed'" class="mt-2">
              <div class="bg-red-50 border border-red-200 rounded-md p-3">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <svg class="h-5 w-5 text-red-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-sm text-red-700">分析失败，请检查视频文件或重试</span>
                  </div>
                  <button
                    @click="retryAnalysis"
                    :disabled="retrying"
                    class="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  >
                    <span v-if="retrying" class="flex items-center">
                      <svg class="animate-spin -ml-1 mr-2 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      重试中...
                    </span>
                    <span v-else>重试分析</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分析结果标签页 -->
      <div class="bg-white rounded-lg shadow">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="switchTab(tab.id, tab.step)"
              class="py-4 px-1 border-b-2 font-medium text-sm relative"
              :class="activeTab === tab.id
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
            >
              {{ tab.name }}
              <div v-if="loadingStep && activeTab === tab.id" class="absolute -top-1 -right-1">
                <div class="animate-spin rounded-full h-3 w-3 border-b border-primary-600"></div>
              </div>
            </button>
          </nav>
        </div>

        <div class="p-6">
          <!-- 基础信息分析 -->
          <div v-if="activeTab === 'basic'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 视频元数据 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-3">视频元数据</h3>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">文件大小</span>
                    <span>{{ analysis.basic.file_size }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">编码格式</span>
                    <span>{{ analysis.basic.codec }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">比特率</span>
                    <span>{{ analysis.basic.bitrate }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">音频格式</span>
                    <span>{{ analysis.basic.audio_codec }}</span>
                  </div>
                </div>
              </div>

              <!-- 质量评估 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-3">质量评估</h3>
                <div v-if="analysis.basic.quality.clarity > 0 || analysis.basic.quality.stability > 0 || analysis.basic.quality.saturation > 0" class="space-y-3">
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-500">清晰度</span>
                      <span>{{ analysis.basic.quality.clarity }}分</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-green-500 h-2 rounded-full"
                        :style="{ width: `${analysis.basic.quality.clarity}%` }"
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-500">稳定性</span>
                      <span>{{ analysis.basic.quality.stability }}分</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-blue-500 h-2 rounded-full"
                        :style="{ width: `${analysis.basic.quality.stability}%` }"
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-500">色彩饱和度</span>
                      <span>{{ analysis.basic.quality.saturation }}分</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-purple-500 h-2 rounded-full"
                        :style="{ width: `${analysis.basic.quality.saturation}%` }"
                      ></div>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center text-gray-500 py-4">
                  <p class="text-sm">暂无质量评估数据</p>
                  <p class="text-xs mt-1">点击"基础信息分析"标签页加载数据</p>
                </div>
              </div>

              <!-- 场景切换 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-3">场景切换</h3>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">场景总数</span>
                    <span>{{ analysis.basic.scenes.total }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">平均时长</span>
                    <span>{{ analysis.basic.scenes.avg_duration }}秒</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">最长场景</span>
                    <span>{{ analysis.basic.scenes.max_duration }}秒</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">最短场景</span>
                    <span>{{ analysis.basic.scenes.min_duration }}秒</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 场景时间轴 -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-3">场景时间轴</h3>
              <div class="relative">
                <div class="flex h-8 bg-white rounded overflow-hidden">
                  <div
                    v-for="(scene, index) in analysis.basic.scene_timeline"
                    :key="index"
                    class="flex items-center justify-center text-xs text-white font-medium"
                    :style="{
                      width: `${(scene.duration / analysis.video.duration) * 100}%`,
                      backgroundColor: getSceneColor(index)
                    }"
                  >
                    {{ scene.id }}
                  </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                  点击场景块查看详细信息
                </div>
              </div>
            </div>

            <!-- 比特率统计 -->
            <div v-if="analysis.basic.bitrate_stats" class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-3">比特率统计</h3>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">{{ formatBitrate(analysis.basic.bitrate_stats.avg_bitrate) }}</div>
                  <div class="text-sm text-gray-500">平均比特率</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">{{ formatBitrate(analysis.basic.bitrate_stats.max_bitrate) }}</div>
                  <div class="text-sm text-gray-500">最大比特率</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-orange-600">{{ formatBitrate(analysis.basic.bitrate_stats.min_bitrate) }}</div>
                  <div class="text-sm text-gray-500">最小比特率</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600">{{ analysis.basic.bitrate_stats.num_frames }}</div>
                  <div class="text-sm text-gray-500">总帧数</div>
                </div>
              </div>

              <!-- ASCII 图表显示 -->
              <div v-if="analysis.basic.bitrate_stats.plot_data" class="bg-white rounded p-3">
                <h4 class="text-sm font-medium text-gray-700 mb-2">比特率变化图</h4>
                <pre class="text-xs font-mono text-gray-600 overflow-x-auto">{{ analysis.basic.bitrate_stats.plot_data }}</pre>
              </div>
            </div>
          </div>

          <!-- 内容要素分析 -->
          <div v-if="activeTab === 'content'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- 人物识别 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-3">人物识别</h3>
                <div v-if="analysis.content.characters && analysis.content.characters.length > 0" class="space-y-3">
                  <div
                    v-for="character in analysis.content.characters"
                    :key="character.id"
                    class="flex items-center justify-between p-3 bg-white rounded"
                  >
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">{{ character.name.charAt(0) }}</span>
                      </div>
                      <div>
                        <p class="text-sm font-medium">{{ character.name }}</p>
                        <p class="text-xs text-gray-500">出现 {{ character.appearances || character.appearances_count || 0 }} 次</p>
                      </div>
                    </div>
                    <div class="text-right">
                      <p class="text-sm font-medium">{{ Math.round(character.screen_time || 0) }}秒</p>
                      <p class="text-xs text-gray-500">{{ Math.round((character.confidence || 0) * 100) }}% 置信度</p>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center text-gray-500 py-4">
                  <p class="text-sm">暂无人物识别数据</p>
                  <p class="text-xs mt-1">点击"内容要素分析"标签页加载数据</p>
                </div>
              </div>

              <!-- 场景分析 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-3">场景分析</h3>
                <div v-if="analysis.content.scene_types && analysis.content.scene_types.length > 0" class="space-y-3">
                  <div
                    v-for="sceneType in analysis.content.scene_types"
                    :key="sceneType.type"
                    class="flex items-center justify-between p-3 bg-white rounded"
                  >
                    <div>
                      <p class="text-sm font-medium">{{ sceneType.type }}</p>
                      <p class="text-xs text-gray-500">{{ sceneType.description }}</p>
                    </div>
                    <div class="text-right">
                      <p class="text-sm font-medium">{{ sceneType.count }} 个场景</p>
                      <p class="text-xs text-gray-500">{{ Math.round(sceneType.duration) }}秒</p>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center text-gray-500 py-4">
                  <p class="text-sm">暂无场景分析数据</p>
                  <p class="text-xs mt-1">点击"内容要素分析"标签页加载数据</p>
                </div>
              </div>
            </div>

            <!-- 情感分析 -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-3">情感分析</h3>
              <div v-if="analysis.content.emotions && analysis.content.emotions.length > 0" class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div
                  v-for="emotion in analysis.content.emotions"
                  :key="emotion.type"
                  class="text-center p-3 bg-white rounded"
                >
                  <div class="text-2xl mb-2">{{ getEmotionIcon(emotion.type) }}</div>
                  <p class="text-sm font-medium">{{ getEmotionText(emotion.type) }}</p>
                  <p class="text-xs text-gray-500">{{ Math.round(emotion.percentage) }}%</p>
                </div>
              </div>
              <div v-else class="text-center text-gray-500 py-4">
                <p class="text-sm">暂无情感分析数据</p>
                <p class="text-xs mt-1">点击"内容要素分析"标签页加载数据</p>
              </div>
            </div>
          </div>

          <!-- 剧情分析 -->
          <div v-if="activeTab === 'plot'" class="space-y-6">
            <!-- 对话识别 -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-3">对话识别</h3>
              <div v-if="analysis.plot.dialogues && analysis.plot.dialogues.length > 0" class="space-y-3 max-h-96 overflow-y-auto">
                <div
                  v-for="dialogue in analysis.plot.dialogues"
                  :key="dialogue.id"
                  class="p-3 bg-white rounded"
                >
                  <div class="flex justify-between items-start mb-2">
                    <span class="text-sm font-medium text-blue-600">{{ dialogue.speaker }}</span>
                    <span class="text-xs text-gray-500">{{ formatTime(dialogue.start_time || dialogue.start) }}</span>
                  </div>
                  <p class="text-sm text-gray-700">{{ dialogue.text }}</p>
                  <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                    <span>情感: {{ getEmotionText(dialogue.emotion) }}</span>
                    <span>置信度: {{ Math.round((dialogue.confidence || 0) * 100) }}%</span>
                  </div>
                </div>
              </div>
              <div v-else class="text-center text-gray-500 py-4">
                <p class="text-sm">暂无对话识别数据</p>
                <p class="text-xs mt-1">点击"剧情分析"标签页加载数据</p>
              </div>
            </div>

            <!-- 剧情节点 -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-3">关键剧情节点</h3>
              <div v-if="analysis.plot.plot_points && analysis.plot.plot_points.length > 0" class="space-y-3">
                <div
                  v-for="plotPoint in analysis.plot.plot_points"
                  :key="plotPoint.id"
                  class="p-3 bg-white rounded"
                >
                  <div class="flex justify-between items-start mb-2">
                    <span class="text-sm font-medium" :class="getPlotPointColor(plotPoint.type)">
                      {{ getPlotPointText(plotPoint.type) }}
                    </span>
                    <span class="text-xs text-gray-500">{{ formatTime(plotPoint.timestamp || plotPoint.start) }}</span>
                  </div>
                  <p class="text-sm text-gray-700">{{ plotPoint.description }}</p>
                  <div class="mt-2">
                    <div class="flex justify-between text-xs text-gray-500 mb-1">
                      <span>重要性</span>
                      <span>{{ Math.round((plotPoint.importance || 0) * 100) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1">
                      <div
                        class="bg-red-500 h-1 rounded-full"
                        :style="{ width: `${(plotPoint.importance || 0) * 100}%` }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-center text-gray-500 py-4">
                <p class="text-sm">暂无剧情节点数据</p>
                <p class="text-xs mt-1">点击"剧情分析"标签页加载数据</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">分析结果未找到</h3>
        <p class="mt-1 text-sm text-gray-500">请检查视频ID是否正确</p>
        <div class="mt-6">
          <button
            @click="goBack"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            返回工作台
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useVideoStore } from '@/stores/videos'
import { useClipStore } from '@/stores/clips'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const videoStore = useVideoStore()
const clipStore = useClipStore()

// 响应式数据
const loading = ref(true)
const retrying = ref(false)
const analysis = ref(null)
const activeTab = ref('basic')
const stepResults = ref({})
const loadingStep = ref(false)

// 标签页定义
const tabs = ref([
  { id: 'basic', name: '基础信息分析', step: 'basic_analysis' },
  { id: 'content', name: '内容要素分析', step: 'content_analysis' },
  { id: 'plot', name: '剧情分析', step: 'plot_analysis' }
])

// 方法
const switchTab = async (tabId, step) => {
  activeTab.value = tabId

  // 如果还没有加载这个步骤的数据，则从后端获取
  if (!stepResults.value[step]) {
    loadingStep.value = true
    try {
      const videoId = route.params.videoId
      const stepResult = await videoStore.getAnalysisStep(videoId, step)
      stepResults.value[step] = stepResult

      // 更新analysis对象中对应的数据
      if (stepResult && stepResult.result) {
        updateAnalysisData(step, stepResult.result)
      }
    } catch (error) {
      console.error(`Failed to load ${step}:`, error)
      appStore.showError('加载失败', `无法加载${step}分析结果`)
    } finally {
      loadingStep.value = false
    }
  }
}

// 更新分析数据的辅助函数
const updateAnalysisData = (step, data) => {
  if (!analysis.value) return

  switch (step) {
    case 'basic_analysis':
      if (data.quality) {
        analysis.value.basic.quality = data.quality
      }
      if (data.audio_codec) {
        analysis.value.basic.audio_codec = data.audio_codec
      }
      break

    case 'content_analysis':
      if (data.characters) {
        analysis.value.content.characters = data.characters
      }
      if (data.scene_types) {
        analysis.value.content.scene_types = data.scene_types
      }
      if (data.emotions) {
        analysis.value.content.emotions = data.emotions
      }
      break

    case 'plot_analysis':
      if (data.dialogues) {
        analysis.value.plot.dialogues = data.dialogues
      }
      if (data.plot_points) {
        analysis.value.plot.plot_points = data.plot_points
      }
      break
  }
}

const loadAnalysis = async () => {
  try {
    const videoId = route.params.videoId

    // 获取视频基本信息
    const video = await videoStore.fetchVideo(videoId)

    // 获取场景检测结果
    const scenesData = await videoStore.getScenes(videoId)

    // 获取比特率统计
    const bitrateStats = await videoStore.getBitrateStats(videoId)

    // 计算场景统计
    const scenes = scenesData.data.scenes || []
    const sceneStats = {
      total: scenes.length,
      avg_duration: scenes.length > 0 ? scenes.reduce((sum, s) => sum + s.duration, 0) / scenes.length : 0,
      max_duration: scenes.length > 0 ? Math.max(...scenes.map(s => s.duration)) : 0,
      min_duration: scenes.length > 0 ? Math.min(...scenes.map(s => s.duration)) : 0
    }

    // 初始化分析数据结构，使用默认值
    analysis.value = {
      video: video,
      progress: video.status === 'completed' ? 100 : (video.status === 'analyzing' ? 50 : 0),
      basic: {
        file_size: formatFileSize(video.file_size),
        codec: video.codec || 'Unknown',
        bitrate: formatBitrate(video.bitrate),
        audio_codec: 'AAC', // 默认值，会从分析结果更新
        quality: {
          clarity: 0, // 默认值，会从分析结果更新
          stability: 0,
          saturation: 0
        },
        scenes: {
          total: sceneStats.total,
          avg_duration: Math.round(sceneStats.avg_duration),
          max_duration: Math.round(sceneStats.max_duration),
          min_duration: Math.round(sceneStats.min_duration)
        },
        scene_timeline: scenes.map((scene) => ({
          id: scene.scene_number,
          start_time: scene.start_time,
          duration: scene.duration
        })),
        bitrate_stats: bitrateStats.data
      },
      content: {
        characters: [], // 默认空数组，会从分析结果更新
        scene_types: [],
        emotions: []
      },
      plot: {
        dialogues: [], // 默认空数组，会从分析结果更新
        plot_points: []
      }
    }

    // 尝试加载已有的分析结果
    try {
      const allResults = await videoStore.getAnalysisResults(videoId)
      if (allResults && Array.isArray(allResults)) {
        allResults.forEach(result => {
          stepResults.value[result.step] = result
          if (result.result) {
            updateAnalysisData(result.step, result.result)
          }
        })
      }
    } catch (error) {
      console.log('No existing analysis results found, will load on demand')
    }

  } catch (error) {
    console.error('Failed to load analysis:', error)
    appStore.showError('加载失败', '无法加载分析结果')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/analysis')
}

const generateClips = async () => {
  try {
    const videoId = route.params.videoId
    await clipStore.generateClips(videoId)
    appStore.showSuccess('生成中', '正在基于分析结果生成智能片段...')
    router.push('/clips')
  } catch (error) {
    appStore.showError('生成失败', '无法生成智能片段')
  }
}

const exportReport = () => {
  appStore.showSuccess('导出中', '正在生成分析报告...')
}

// 重试分析
const retryAnalysis = async () => {
  try {
    retrying.value = true
    const videoId = route.params.videoId
    await videoStore.retryAnalysis(videoId)

    // 重新加载分析数据
    await loadAnalysis()

    appStore.showSuccess('重试成功', '分析任务已重新启动')
  } catch (error) {
    appStore.showError('重试失败', '无法重新启动分析任务')
  } finally {
    retrying.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classes = {
    uploaded: 'bg-gray-100 text-gray-800',
    analyzing: 'bg-blue-100 text-blue-800',
    analyzed: 'bg-green-100 text-green-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    uploaded: '已上传',
    analyzing: '分析中',
    analyzed: '已分析',
    completed: '已完成',
    failed: '分析失败'
  }
  return texts[status] || '未知状态'
}

const getSceneColor = (index) => {
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']
  return colors[index % colors.length]
}

const getEmotionIcon = (emotion) => {
  const icons = {
    happy: '😊',
    sad: '😢',
    angry: '😠',
    nervous: '😰',
    excited: '🤩',
    calm: '😌'
  }
  return icons[emotion] || '😐'
}

const getEmotionText = (emotion) => {
  const texts = {
    happy: '快乐',
    sad: '悲伤',
    angry: '愤怒',
    nervous: '紧张',
    excited: '兴奋',
    calm: '平静'
  }
  return texts[emotion] || emotion
}

const getPlotPointText = (type) => {
  const texts = {
    introduction: '开场介绍',
    conflict: '冲突爆发',
    climax: '故事高潮',
    resolution: '结局收尾'
  }
  return texts[type] || type
}

const getPlotPointColor = (type) => {
  const colors = {
    introduction: 'text-blue-600',
    conflict: 'text-orange-600',
    climax: 'text-red-600',
    resolution: 'text-green-600'
  }
  return colors[type] || 'text-gray-600'
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatFileSize = (bytes) => {
  if (!bytes) return 'Unknown'
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

const formatBitrate = (bitrate) => {
  if (!bitrate) return 'Unknown'
  if (bitrate >= 1000000) {
    return `${(bitrate / 1000000).toFixed(1)} Mbps`
  } else if (bitrate >= 1000) {
    return `${(bitrate / 1000).toFixed(1)} Kbps`
  } else {
    return `${bitrate} bps`
  }
}

// 生命周期
onMounted(() => {
  loadAnalysis()
})
</script>
